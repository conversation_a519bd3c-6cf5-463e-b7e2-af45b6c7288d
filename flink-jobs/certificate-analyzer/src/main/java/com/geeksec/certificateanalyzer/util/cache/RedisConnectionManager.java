package com.geeksec.certificateanalyzer.util.cache;

import lombok.extern.slf4j.Slf4j;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.JedisPoolConfig;

/**
 * Redis连接管理器
 * 
 * <AUTHOR>
 */
@Slf4j
public class RedisConnectionManager {

    private static JedisPool jedisPool;
    private static String redisHost;
    private static int redisPort;
    private static String redisPassword;
    private static int redisDatabase;
    
    static {
        // 从环境变量或配置文件加载Redis连接信息
        loadRedisConfig();
        initializeJedisPool();
    }

    /**
     * 获取Jedis连接池
     */
    public static JedisPool getJedisPool() {
        if (jedisPool == null || jedisPool.isClosed()) {
            synchronized (RedisConnectionManager.class) {
                if (jedisPool == null || jedisPool.isClosed()) {
                    initializeJedisPool();
                }
            }
        }
        return jedisPool;
    }

    /**
     * 初始化Jedis连接池
     */
    private static void initializeJedisPool() {
        try {
            JedisPoolConfig config = new JedisPoolConfig();
            
            // 连接池配置
            config.setMaxTotal(50);                 // 最大连接数
            config.setMaxIdle(10);                  // 最大空闲连接数
            config.setMinIdle(5);                   // 最小空闲连接数
            config.setMaxWaitMillis(3000);          // 获取连接时的最大等待毫秒数
            config.setTestOnBorrow(true);           // 在获取连接的时候检查有效性
            config.setTestOnReturn(true);           // 在归还连接的时候检查有效性
            config.setTestWhileIdle(true);          // 在空闲时检查有效性
            config.setTimeBetweenEvictionRunsMillis(30000); // 逐出扫描的时间间隔
            config.setNumTestsPerEvictionRun(3);    // 每次逐出检查时逐出的最大数目
            config.setMinEvictableIdleTimeMillis(60000);    // 连接保持空闲而不被驱逐的最小时间
            
            // 创建连接池
            if (redisPassword != null && !redisPassword.trim().isEmpty()) {
                jedisPool = new JedisPool(config, redisHost, redisPort, 3000, redisPassword, redisDatabase);
            } else {
                jedisPool = new JedisPool(config, redisHost, redisPort, 3000, null, redisDatabase);
            }
            
            log.info("Redis连接池初始化完成: Host={}, Port={}, Database={}", redisHost, redisPort, redisDatabase);
            
        } catch (Exception e) {
            log.error("Redis连接池初始化失败", e);
            throw new RuntimeException("Redis连接池初始化失败", e);
        }
    }

    /**
     * 加载Redis配置
     */
    private static void loadRedisConfig() {
        // 从环境变量加载配置
        redisHost = System.getenv("REDIS_HOST");
        String redisPortStr = System.getenv("REDIS_PORT");
        redisPassword = System.getenv("REDIS_PASSWORD");
        String redisDatabaseStr = System.getenv("REDIS_DATABASE");
        
        // 如果环境变量未设置，使用默认值
        if (redisHost == null || redisHost.trim().isEmpty()) {
            redisHost = "localhost";
        }
        
        if (redisPortStr == null || redisPortStr.trim().isEmpty()) {
            redisPort = 6379;
        } else {
            try {
                redisPort = Integer.parseInt(redisPortStr);
            } catch (NumberFormatException e) {
                log.warn("Redis端口配置无效，使用默认端口6379: {}", redisPortStr);
                redisPort = 6379;
            }
        }
        
        if (redisDatabaseStr == null || redisDatabaseStr.trim().isEmpty()) {
            redisDatabase = 0;
        } else {
            try {
                redisDatabase = Integer.parseInt(redisDatabaseStr);
            } catch (NumberFormatException e) {
                log.warn("Redis数据库配置无效，使用默认数据库0: {}", redisDatabaseStr);
                redisDatabase = 0;
            }
        }
        
        log.info("Redis配置加载完成: Host={}, Port={}, Database={}", redisHost, redisPort, redisDatabase);
    }

    /**
     * 关闭连接池
     */
    public static void closeJedisPool() {
        if (jedisPool != null && !jedisPool.isClosed()) {
            jedisPool.close();
            log.info("Redis连接池已关闭");
        }
    }
}
